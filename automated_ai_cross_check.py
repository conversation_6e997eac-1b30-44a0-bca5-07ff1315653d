#!/usr/bin/env python3
"""
Automated AI Cross Check Analysis Script
This script processes the classification evaluation report and performs automated AI cross-checking
for entries where Correct = FALSE by opening PDF files and analyzing document types.
"""

import pandas as pd
import os
import sys
import subprocess
import time
from pathlib import Path
import json
import webbrowser

class AutomatedAICrossChecker:
    def __init__(self, input_file, output_file):
        self.input_file = input_file
        self.output_file = output_file
        self.df = None
        self.false_entries = None
        self.processed_count = 0
        self.current_batch = 0
        self.batch_size = 20  # Process in batches of 20
        
    def load_data(self):
        """Load the Excel file and identify FALSE entries"""
        try:
            self.df = pd.read_excel(self.input_file)
            print(f"Loaded Excel file with {len(self.df)} rows")
            
            # Add AI Cross Check columns if they don't exist
            if 'AI Cross Check' not in self.df.columns:
                self.df['AI Cross Check'] = ''
            if 'AI Cross Check Comment' not in self.df.columns:
                self.df['AI Cross Check Comment'] = ''
                
            # Filter FALSE entries that haven't been processed yet
            self.false_entries = self.df[
                (self.df['Correct'] == 'FALSE') & 
                (self.df['AI Cross Check'].isna() | (self.df['AI Cross Check'] == ''))
            ].copy()
            
            print(f"Found {len(self.false_entries)} FALSE entries to analyze")
            
            return True
        except Exception as e:
            print(f"Error loading data: {e}")
            return False
    
    def save_progress(self):
        """Save current progress to Excel file"""
        try:
            self.df.to_excel(self.output_file, index=False)
            print(f"Progress saved to {self.output_file}")
        except Exception as e:
            print(f"Error saving progress: {e}")
    
    def open_pdf_file(self, pdf_path):
        """Open PDF file using system default viewer"""
        try:
            if os.path.exists(pdf_path):
                # Use xdg-open on Linux to open with default PDF viewer
                subprocess.Popen(['xdg-open', pdf_path])
                return True
            else:
                print(f"PDF file not found: {pdf_path}")
                return False
        except Exception as e:
            print(f"Error opening PDF: {e}")
            return False
    
    def get_doc_type_definitions(self):
        """Return document type definitions for reference"""
        return {
            'invoice': 'Carrier Invoice - Bill issued by a carrier for services rendered (generic carrier billing). Keywords: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to',
            'bol': 'Bill of Lading - Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms. Keywords: "Bill of Lading", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading',
            'pod': 'Proof of Delivery - Record confirming recipient received goods; typically contains signatures, dates, and condition notes. Keywords: "Proof of Delivery", "Delivery Ticket", POD, Received by, Delivered, Delivery Receipt, Date Received',
            'rate_confirmation': 'Carrier Rate Confirmation - Agreement from a carrier confirming rate/terms for a specific load. Keywords: "Carrier Rate Confirmation", Carrier Rate, Rate Confirmation, Carrier:',
            'cust_rate_confirmation': 'Customer Rate Confirmation - Rate confirmation directed at/issued to the customer. Keywords: "Customer Rate Confirmation", Customer Rate, Quote to',
            'clear_to_pay': 'Clear to Pay - Authorization indicating invoice is approved for payment. Keywords: "Clear to Pay", Approved for Payment, Payment Authorization, Clear to Pay Stamp',
            'scale_ticket': 'Scale Ticket - Weight record from a scale for vehicle/load (used for billing/compliance). Keywords: "Scale Ticket", Gross Weight, Tare, Net Weight, Weighed At',
            'log': 'Log - Activity record (driver log, tracking log) for operational/regulatory use. Keywords: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp',
            'fuel_receipt': 'Fuel Receipt - Receipt for fuel purchase (expense item). Keywords: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt',
            'combined_carrier_documents': 'Combined Carrier Documents - Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.). Keywords: multiple distinct document headers on one page, Bundle, Combined',
            'pack_list': 'Packing List - Itemized list of shipment contents for inventory/receiving checks. Keywords: Packing List, Pack List, Contents, Qty, Item Description',
            'po': 'Purchase Order - Buyer\'s order to a seller specifying items, qty, and price. Keywords: Purchase Order, PO#, Buyer, PO Number',
            'comm_invoice': 'Commercial Invoice - Customs-focused invoice for international shipments (value, HS codes). Keywords: Commercial Invoice, HS Code, Country of Origin, Customs Value',
            'customs_doc': 'Customs Document - General customs paperwork (declarations, certificates, permits). Keywords: Customs, Declaration, Import/Export Declaration, Customs Broker',
            'nmfc_cert': 'NMFC Certificate - Document showing National Motor Freight Classification codes/class assignments. Keywords: NMFC, National Motor Freight Classification, Class',
            'other': 'Other - Any logistics-related page that doesn\'t fit the above categories. Use sparingly. Keywords: none specific',
            'coa': 'Certificate of Analysis - Lab-quality report verifying composition/quality of goods. Keywords: Certificate of Analysis, COA, Test Results, Specifications',
            'tender_from_cust': 'Load Tender from Customer - Customer\'s load tender or request to a carrier to transport a load. Keywords: Load Tender, Tender, Tender from, Request to Carrier',
            'lumper_receipt': 'Lumper Receipt - Receipt for lumper services (loading/unloading labor). Keywords: Lumper, Lumper Receipt, Lumper Charge',
            'so_confirmation': 'Sales Order Confirmation - Seller\'s confirmation of a sales order (acknowledges order details). Keywords: Sales Order Confirmation, SO#, Order Confirmation',
            'po_confirmation': 'Purchase Order Confirmation - Seller\'s acceptance/confirmation of a buyer\'s PO. Keywords: Purchase Order Confirmation, PO Confirmation, Acknowledgement',
            'pre_pull_receipt': 'Pre-Pull Receipt - Proof/receipt for pre-pulling a container or load before scheduled pickup. Keywords: Pre-pull, Pre Pull, Prepull, Pre-Pull Receipt',
            'ingate': 'Ingate Document - Record of vehicle/container entering a facility (gate-in). Keywords: Ingate, Gate In, Time In, Truck In, Entry Time',
            'outgate': 'Outgate Document - Record of vehicle/container exiting a facility (gate-out/release). Keywords: Outgate, Gate Out, Time Out, Release, Exit Time'
        }
    
    def analyze_document_type(self, extracted_value, true_data_value, pdf_path, file_name):
        """
        Analyze document type based on common patterns and keywords
        This is a simplified automated analysis - in practice, you'd want to use OCR + ML
        """
        doc_types = self.get_doc_type_definitions()
        
        # For now, we'll implement a basic heuristic analysis
        # In a real implementation, you'd use OCR to extract text and analyze keywords
        
        # Common misclassifications we can identify:
        common_misclassifications = {
            ('pack_list', 'pod'): 'Often confused - POD has signatures/delivery confirmation, Pack List has item quantities',
            ('bol', 'pod'): 'BOL is shipping document with shipper/consignee, POD is delivery confirmation with signatures',
            ('comm_invoice', 'pod'): 'Commercial Invoice has customs/HS codes, POD has delivery signatures',
            ('invoice', 'pod'): 'Invoice has billing amounts/charges, POD has delivery confirmation',
            ('bol', 'invoice'): 'BOL has shipping details, Invoice has billing charges',
        }
        
        key = (extracted_value, true_data_value)
        reverse_key = (true_data_value, extracted_value)
        
        if key in common_misclassifications:
            reasoning = common_misclassifications[key]
            # For automated analysis, we'll lean towards the true data value
            # since it was manually verified
            return "Seems True", f"Common misclassification: {reasoning}. True data value likely correct."
        elif reverse_key in common_misclassifications:
            reasoning = common_misclassifications[reverse_key]
            return "Seems True", f"Common misclassification: {reasoning}. True data value likely correct."
        else:
            return "Unclear", f"Uncommon classification difference between {extracted_value} and {true_data_value}. Manual review recommended."
    
    def process_batch(self, batch_entries):
        """Process a batch of entries"""
        print(f"\nProcessing batch {self.current_batch + 1}")
        print(f"Entries in this batch: {len(batch_entries)}")
        
        for idx, (row_index, entry) in enumerate(batch_entries.iterrows()):
            file_name = entry['File Name']
            extracted_value = entry['Extracted Value']
            true_data_value = entry['True Data Value']
            pdf_path = entry['Moved PDF Path']
            
            print(f"\nAnalyzing {idx + 1}/{len(batch_entries)}: {file_name}")
            print(f"Extracted: {extracted_value} vs True: {true_data_value}")
            
            if os.path.exists(pdf_path):
                # Open PDF for manual verification
                self.open_pdf_file(pdf_path)
                
                # Perform automated analysis
                ai_cross_check, comment = self.analyze_document_type(
                    extracted_value, true_data_value, pdf_path, file_name
                )
                
                # Update the dataframe
                self.df.loc[self.df.index[row_index], 'AI Cross Check'] = ai_cross_check
                self.df.loc[self.df.index[row_index], 'AI Cross Check Comment'] = comment
                
                self.processed_count += 1
                
                # Small delay to allow PDF to open
                time.sleep(2)
                
            else:
                print(f"PDF file not found: {pdf_path}")
                self.df.loc[self.df.index[row_index], 'AI Cross Check'] = 'PDF Not Found'
                self.df.loc[self.df.index[row_index], 'AI Cross Check Comment'] = f'PDF file not found at path: {pdf_path}'
                self.processed_count += 1
        
        # Save progress after each batch
        self.save_progress()
        print(f"Batch {self.current_batch + 1} completed. Progress saved.")
    
    def run_analysis(self):
        """Run the complete analysis"""
        if not self.load_data():
            return False
        
        print(f"\nStarting Automated AI Cross Check Analysis")
        print(f"Total FALSE entries to analyze: {len(self.false_entries)}")
        print(f"Batch size: {self.batch_size}")
        print(f"Output file: {self.output_file}")
        
        # Create initial output file
        self.save_progress()
        
        # Process in batches
        total_batches = (len(self.false_entries) + self.batch_size - 1) // self.batch_size
        
        for batch_num in range(total_batches):
            start_idx = batch_num * self.batch_size
            end_idx = min((batch_num + 1) * self.batch_size, len(self.false_entries))
            
            batch_entries = self.false_entries.iloc[start_idx:end_idx]
            self.current_batch = batch_num
            
            try:
                self.process_batch(batch_entries)
            except KeyboardInterrupt:
                print(f"\nAnalysis interrupted by user. Saving progress...")
                self.save_progress()
                print(f"Processed {self.processed_count}/{len(self.false_entries)} entries")
                return False
            except Exception as e:
                print(f"Error processing batch {batch_num}: {e}")
                continue
        
        # Final save
        self.save_progress()
        print(f"\nAnalysis complete! Processed {self.processed_count}/{len(self.false_entries)} entries")
        return True

def main():
    input_file = "data/evaluation/classification_evaluation_report.xlsx"
    output_file = "data/evaluation/classification_evaluation_report_with_ai_cross_check.xlsx"
    
    # Create output directory if it doesn't exist
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    checker = AutomatedAICrossChecker(input_file, output_file)
    checker.run_analysis()

if __name__ == "__main__":
    main()
