#!/usr/bin/env python3
"""
AI Cross Check Analysis Script
This script processes the classification evaluation report and performs AI cross-checking
for entries where Correct = FALSE by opening PDF files and analyzing document types.
"""

import pandas as pd
import os
import sys
import subprocess
import time
from pathlib import Path
import json

class AIClassificationCrossChecker:
    def __init__(self, input_file, output_file):
        self.input_file = input_file
        self.output_file = output_file
        self.df = None
        self.false_entries = None
        self.processed_count = 0
        
    def load_data(self):
        """Load the Excel file and identify FALSE entries"""
        try:
            self.df = pd.read_excel(self.input_file)
            print(f"Loaded Excel file with {len(self.df)} rows")
            
            # Add AI Cross Check columns if they don't exist
            if 'AI Cross Check' not in self.df.columns:
                self.df['AI Cross Check'] = ''
            if 'AI Cross Check Comment' not in self.df.columns:
                self.df['AI Cross Check Comment'] = ''
                
            # Filter FALSE entries
            self.false_entries = self.df[self.df['Correct'] == 'FALSE'].copy()
            print(f"Found {len(self.false_entries)} FALSE entries to analyze")
            
            return True
        except Exception as e:
            print(f"Error loading data: {e}")
            return False
    
    def save_progress(self):
        """Save current progress to Excel file"""
        try:
            self.df.to_excel(self.output_file, index=False)
            print(f"Progress saved to {self.output_file}")
        except Exception as e:
            print(f"Error saving progress: {e}")
    
    def open_pdf_file(self, pdf_path):
        """Open PDF file using system default viewer"""
        try:
            if os.path.exists(pdf_path):
                # Use xdg-open on Linux to open with default PDF viewer
                subprocess.run(['xdg-open', pdf_path], check=True)
                return True
            else:
                print(f"PDF file not found: {pdf_path}")
                return False
        except Exception as e:
            print(f"Error opening PDF: {e}")
            return False
    
    def get_doc_type_definitions(self):
        """Return document type definitions for reference"""
        return {
            'invoice': 'Carrier Invoice - Bill issued by a carrier for services rendered',
            'bol': 'Bill of Lading - Legal transport document issued by carrier to shipper',
            'pod': 'Proof of Delivery - Record confirming recipient received goods',
            'rate_confirmation': 'Carrier Rate Confirmation - Agreement from carrier confirming rate/terms',
            'cust_rate_confirmation': 'Customer Rate Confirmation - Rate confirmation directed at customer',
            'clear_to_pay': 'Clear to Pay - Authorization indicating invoice is approved for payment',
            'scale_ticket': 'Scale Ticket - Weight record from a scale for vehicle/load',
            'log': 'Log - Activity record (driver log, tracking log)',
            'fuel_receipt': 'Fuel Receipt - Receipt for fuel purchase',
            'combined_carrier_documents': 'Combined Carrier Documents - Multiple carrier documents bundled together',
            'pack_list': 'Packing List - Itemized list of shipment contents',
            'po': 'Purchase Order - Buyer\'s order to a seller specifying items, qty, and price',
            'comm_invoice': 'Commercial Invoice - Customs-focused invoice for international shipments',
            'customs_doc': 'Customs Document - General customs paperwork',
            'nmfc_cert': 'NMFC Certificate - Document showing National Motor Freight Classification codes',
            'other': 'Other - Any logistics-related page that doesn\'t fit other categories',
            'coa': 'Certificate of Analysis - Lab-quality report verifying composition/quality',
            'tender_from_cust': 'Load Tender from Customer - Customer\'s load tender or request to carrier',
            'lumper_receipt': 'Lumper Receipt - Receipt for lumper services',
            'so_confirmation': 'Sales Order Confirmation - Seller\'s confirmation of a sales order',
            'po_confirmation': 'Purchase Order Confirmation - Seller\'s acceptance/confirmation of buyer\'s PO',
            'pre_pull_receipt': 'Pre-Pull Receipt - Proof/receipt for pre-pulling a container',
            'ingate': 'Ingate Document - Record of vehicle/container entering a facility',
            'outgate': 'Outgate Document - Record of vehicle/container exiting a facility'
        }
    
    def analyze_entry(self, row_index, entry):
        """Analyze a single FALSE entry"""
        file_name = entry['File Name']
        extracted_value = entry['Extracted Value']
        true_data_value = entry['True Data Value']
        pdf_path = entry['Moved PDF Path']
        
        print(f"\n{'='*60}")
        print(f"Analyzing Entry {self.processed_count + 1}/{len(self.false_entries)}")
        print(f"File: {file_name}")
        print(f"Extracted Value: {extracted_value}")
        print(f"True Data Value: {true_data_value}")
        print(f"PDF Path: {pdf_path}")
        print(f"{'='*60}")
        
        # Get document type definitions
        doc_types = self.get_doc_type_definitions()
        
        print("\nDocument Type Definitions:")
        if extracted_value in doc_types:
            print(f"Extracted ({extracted_value}): {doc_types[extracted_value]}")
        if true_data_value in doc_types:
            print(f"True Data ({true_data_value}): {doc_types[true_data_value]}")
        
        # Open PDF file
        if os.path.exists(pdf_path):
            print(f"\nOpening PDF file: {pdf_path}")
            self.open_pdf_file(pdf_path)
            
            # Wait for user to analyze the document
            print("\nPlease analyze the PDF document and determine which classification is correct.")
            print("Options:")
            print("1. 'Seems True' - if the True Data Value appears correct")
            print("2. 'Seems False' - if the Extracted Value appears correct")
            print("3. 'Unclear' - if it's difficult to determine")
            
            while True:
                ai_cross_check = input("\nEnter AI Cross Check result (Seems True/Seems False/Unclear): ").strip()
                if ai_cross_check.lower() in ['seems true', 'seems false', 'unclear']:
                    break
                print("Please enter 'Seems True', 'Seems False', or 'Unclear'")
            
            comment = input("Enter AI Cross Check Comment (describe your reasoning): ").strip()
            
            # Update the dataframe
            self.df.loc[self.df.index[row_index], 'AI Cross Check'] = ai_cross_check
            self.df.loc[self.df.index[row_index], 'AI Cross Check Comment'] = comment
            
            self.processed_count += 1
            
            # Save progress every 10 entries
            if self.processed_count % 10 == 0:
                self.save_progress()
                print(f"\nProgress saved! Processed {self.processed_count}/{len(self.false_entries)} entries")
            
        else:
            print(f"PDF file not found: {pdf_path}")
            self.df.loc[self.df.index[row_index], 'AI Cross Check'] = 'PDF Not Found'
            self.df.loc[self.df.index[row_index], 'AI Cross Check Comment'] = f'PDF file not found at path: {pdf_path}'
            self.processed_count += 1
    
    def run_analysis(self):
        """Run the complete analysis"""
        if not self.load_data():
            return False
        
        print(f"\nStarting AI Cross Check Analysis")
        print(f"Total FALSE entries to analyze: {len(self.false_entries)}")
        print(f"Output file: {self.output_file}")
        
        # Create initial output file
        self.save_progress()
        
        # Process each FALSE entry
        for idx, (row_index, entry) in enumerate(self.false_entries.iterrows()):
            try:
                self.analyze_entry(row_index, entry)
            except KeyboardInterrupt:
                print(f"\nAnalysis interrupted by user. Saving progress...")
                self.save_progress()
                print(f"Processed {self.processed_count}/{len(self.false_entries)} entries")
                return False
            except Exception as e:
                print(f"Error analyzing entry {idx}: {e}")
                continue
        
        # Final save
        self.save_progress()
        print(f"\nAnalysis complete! Processed {self.processed_count}/{len(self.false_entries)} entries")
        return True

def main():
    input_file = "data/evaluation/classification_evaluation_report.xlsx"
    output_file = "data/evaluation/classification_evaluation_report_with_ai_cross_check.xlsx"
    
    # Create output directory if it doesn't exist
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    checker = AIClassificationCrossChecker(input_file, output_file)
    checker.run_analysis()

if __name__ == "__main__":
    main()
