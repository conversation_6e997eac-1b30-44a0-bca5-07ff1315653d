#!/usr/bin/env python3
"""
Preserve Formatting AI Cross Check Script
This script adds AI Cross Check columns while preserving all original Excel formatting,
hyperlinks, and styling from the original file.
"""

import pandas as pd
import openpyxl
from openpyxl.styles import <PERSON>ont, PatternFill, Border, Side, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows
import os
import sys

class FormattingPreservingCrossChecker:
    def __init__(self, input_file, output_file):
        self.input_file = input_file
        self.output_file = output_file
        self.df = None
        self.workbook = None
        self.worksheet = None
        
    def load_original_workbook(self):
        """Load the original Excel workbook to preserve formatting"""
        try:
            self.workbook = openpyxl.load_workbook(self.input_file)
            self.worksheet = self.workbook.active
            print(f"Loaded original workbook with formatting preserved")
            return True
        except Exception as e:
            print(f"Error loading workbook: {e}")
            return False
    
    def load_ai_analysis_data(self):
        """Load the AI analysis results"""
        try:
            # Load our completed analysis
            analysis_file = "data/evaluation/classification_evaluation_report_with_ai_cross_check_fast.xlsx"
            self.df = pd.read_excel(analysis_file)
            print(f"Loaded AI analysis data with {len(self.df)} rows")
            return True
        except Exception as e:
            print(f"Error loading AI analysis data: {e}")
            return False
    
    def add_ai_columns_with_formatting(self):
        """Add AI Cross Check columns while preserving original formatting"""
        try:
            # Get the current number of columns
            max_col = self.worksheet.max_column
            max_row = self.worksheet.max_row
            
            # Add headers for new columns
            ai_cross_check_col = max_col + 1
            ai_comment_col = max_col + 2
            
            # Add headers with same formatting as existing headers
            header_row = 1
            
            # Copy formatting from the last existing header
            last_header_cell = self.worksheet.cell(row=header_row, column=max_col)
            
            # Add "AI Cross Check" header
            ai_check_header = self.worksheet.cell(row=header_row, column=ai_cross_check_col)
            ai_check_header.value = "AI Cross Check"
            if last_header_cell.font:
                ai_check_header.font = Font(
                    name=last_header_cell.font.name,
                    size=last_header_cell.font.size,
                    bold=last_header_cell.font.bold,
                    italic=last_header_cell.font.italic,
                    color=last_header_cell.font.color
                )
            if last_header_cell.fill:
                ai_check_header.fill = PatternFill(
                    start_color=last_header_cell.fill.start_color,
                    end_color=last_header_cell.fill.end_color,
                    fill_type=last_header_cell.fill.fill_type
                )
            if last_header_cell.border:
                ai_check_header.border = Border(
                    left=last_header_cell.border.left,
                    right=last_header_cell.border.right,
                    top=last_header_cell.border.top,
                    bottom=last_header_cell.border.bottom
                )
            if last_header_cell.alignment:
                ai_check_header.alignment = Alignment(
                    horizontal=last_header_cell.alignment.horizontal,
                    vertical=last_header_cell.alignment.vertical,
                    wrap_text=last_header_cell.alignment.wrap_text
                )
            
            # Add "AI Cross Check Comment" header
            ai_comment_header = self.worksheet.cell(row=header_row, column=ai_comment_col)
            ai_comment_header.value = "AI Cross Check Comment"
            if last_header_cell.font:
                ai_comment_header.font = Font(
                    name=last_header_cell.font.name,
                    size=last_header_cell.font.size,
                    bold=last_header_cell.font.bold,
                    italic=last_header_cell.font.italic,
                    color=last_header_cell.font.color
                )
            if last_header_cell.fill:
                ai_comment_header.fill = PatternFill(
                    start_color=last_header_cell.fill.start_color,
                    end_color=last_header_cell.fill.end_color,
                    fill_type=last_header_cell.fill.fill_type
                )
            if last_header_cell.border:
                ai_comment_header.border = Border(
                    left=last_header_cell.border.left,
                    right=last_header_cell.border.right,
                    top=last_header_cell.border.top,
                    bottom=last_header_cell.border.bottom
                )
            if last_header_cell.alignment:
                ai_comment_header.alignment = Alignment(
                    horizontal=last_header_cell.alignment.horizontal,
                    vertical=last_header_cell.alignment.vertical,
                    wrap_text=True  # Enable text wrapping for comments
                )
            
            # Create a mapping from File Name to AI analysis results
            ai_data_map = {}
            for _, row in self.df.iterrows():
                key = (row['File Name'], row['Field Name'])
                ai_data_map[key] = {
                    'ai_cross_check': row.get('AI Cross Check', ''),
                    'ai_comment': row.get('AI Cross Check Comment', '')
                }
            
            # Add data to the new columns
            for row_num in range(2, max_row + 1):  # Start from row 2 (skip header)
                # Get File Name and Field Name from current row
                file_name_cell = self.worksheet.cell(row=row_num, column=1)  # Assuming File Name is column 1
                field_name_cell = self.worksheet.cell(row=row_num, column=2)  # Assuming Field Name is column 2
                
                file_name = file_name_cell.value
                field_name = field_name_cell.value
                
                if file_name and field_name:
                    key = (file_name, field_name)
                    if key in ai_data_map:
                        # Add AI Cross Check value
                        ai_check_cell = self.worksheet.cell(row=row_num, column=ai_cross_check_col)
                        ai_check_cell.value = ai_data_map[key]['ai_cross_check']
                        
                        # Add AI Cross Check Comment
                        ai_comment_cell = self.worksheet.cell(row=row_num, column=ai_comment_col)
                        ai_comment_cell.value = ai_data_map[key]['ai_comment']
                        
                        # Copy formatting from adjacent cell
                        adjacent_cell = self.worksheet.cell(row=row_num, column=max_col)
                        
                        # Apply formatting to AI Cross Check cell
                        if adjacent_cell.font:
                            ai_check_cell.font = Font(
                                name=adjacent_cell.font.name,
                                size=adjacent_cell.font.size,
                                bold=adjacent_cell.font.bold,
                                italic=adjacent_cell.font.italic,
                                color=adjacent_cell.font.color
                            )
                        if adjacent_cell.border:
                            ai_check_cell.border = Border(
                                left=adjacent_cell.border.left,
                                right=adjacent_cell.border.right,
                                top=adjacent_cell.border.top,
                                bottom=adjacent_cell.border.bottom
                            )
                        if adjacent_cell.alignment:
                            ai_check_cell.alignment = Alignment(
                                horizontal=adjacent_cell.alignment.horizontal,
                                vertical=adjacent_cell.alignment.vertical,
                                wrap_text=adjacent_cell.alignment.wrap_text
                            )
                        
                        # Apply formatting to AI Comment cell
                        if adjacent_cell.font:
                            ai_comment_cell.font = Font(
                                name=adjacent_cell.font.name,
                                size=adjacent_cell.font.size,
                                bold=adjacent_cell.font.bold,
                                italic=adjacent_cell.font.italic,
                                color=adjacent_cell.font.color
                            )
                        if adjacent_cell.border:
                            ai_comment_cell.border = Border(
                                left=adjacent_cell.border.left,
                                right=adjacent_cell.border.right,
                                top=adjacent_cell.border.top,
                                bottom=adjacent_cell.border.bottom
                            )
                        if adjacent_cell.alignment:
                            ai_comment_cell.alignment = Alignment(
                                horizontal=adjacent_cell.alignment.horizontal,
                                vertical=adjacent_cell.alignment.vertical,
                                wrap_text=True  # Enable text wrapping for comments
                            )
            
            # Adjust column widths
            self.worksheet.column_dimensions[openpyxl.utils.get_column_letter(ai_cross_check_col)].width = 15
            self.worksheet.column_dimensions[openpyxl.utils.get_column_letter(ai_comment_col)].width = 50
            
            print(f"Added AI Cross Check columns with preserved formatting")
            return True
            
        except Exception as e:
            print(f"Error adding AI columns: {e}")
            return False
    
    def save_workbook(self):
        """Save the workbook with preserved formatting"""
        try:
            self.workbook.save(self.output_file)
            print(f"Saved workbook with preserved formatting to: {self.output_file}")
            return True
        except Exception as e:
            print(f"Error saving workbook: {e}")
            return False
    
    def run_analysis(self):
        """Run the complete analysis while preserving formatting"""
        print("Starting formatting-preserving AI Cross Check analysis...")
        
        if not self.load_original_workbook():
            return False
        
        if not self.load_ai_analysis_data():
            return False
        
        if not self.add_ai_columns_with_formatting():
            return False
        
        if not self.save_workbook():
            return False
        
        print("Analysis complete with formatting preserved!")
        return True

def main():
    input_file = "data/evaluation/classification_evaluation_report.xlsx"
    output_file = "data/evaluation/classification_evaluation_report_FINAL_WITH_FORMATTING.xlsx"
    
    # Create output directory if it doesn't exist
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    checker = FormattingPreservingCrossChecker(input_file, output_file)
    success = checker.run_analysis()
    
    if success:
        print(f"\n✅ SUCCESS!")
        print(f"Created: {output_file}")
        print(f"• All original formatting preserved")
        print(f"• All hyperlinks preserved") 
        print(f"• Added AI Cross Check columns")
        print(f"• Ready for use!")
    else:
        print(f"\n❌ FAILED!")
        print(f"Please check the error messages above.")

if __name__ == "__main__":
    main()
