#!/usr/bin/env python3
"""
Fast AI Cross Check Analysis Script
This script processes the classification evaluation report and performs automated AI cross-checking
for entries where Correct = FALSE without opening PDF files for faster processing.
"""

import pandas as pd
import os
import sys
import time
from pathlib import Path

class FastAICrossChecker:
    def __init__(self, input_file, output_file):
        self.input_file = input_file
        self.output_file = output_file
        self.df = None
        self.false_entries = None
        self.processed_count = 0
        
    def load_data(self):
        """Load the Excel file and identify FALSE entries"""
        try:
            self.df = pd.read_excel(self.input_file)
            print(f"Loaded Excel file with {len(self.df)} rows")
            
            # Add AI Cross Check columns if they don't exist
            if 'AI Cross Check' not in self.df.columns:
                self.df['AI Cross Check'] = ''
            if 'AI Cross Check Comment' not in self.df.columns:
                self.df['AI Cross Check Comment'] = ''
                
            # Filter FALSE entries that haven't been processed yet
            self.false_entries = self.df[
                (self.df['Correct'] == 'FALSE') & 
                (self.df['AI Cross Check'].isna() | (self.df['AI Cross Check'] == ''))
            ].copy()
            
            print(f"Found {len(self.false_entries)} FALSE entries to analyze")
            
            return True
        except Exception as e:
            print(f"Error loading data: {e}")
            return False
    
    def save_progress(self):
        """Save current progress to Excel file"""
        try:
            self.df.to_excel(self.output_file, index=False)
            print(f"Progress saved to {self.output_file}")
        except Exception as e:
            print(f"Error saving progress: {e}")
    
    def get_doc_type_definitions(self):
        """Return document type definitions for reference"""
        return {
            'invoice': 'Carrier Invoice - Bill issued by a carrier for services rendered (generic carrier billing). Keywords: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to',
            'bol': 'Bill of Lading - Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms. Keywords: "Bill of Lading", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading',
            'pod': 'Proof of Delivery - Record confirming recipient received goods; typically contains signatures, dates, and condition notes. Keywords: "Proof of Delivery", "Delivery Ticket", POD, Received by, Delivered, Delivery Receipt, Date Received',
            'rate_confirmation': 'Carrier Rate Confirmation - Agreement from a carrier confirming rate/terms for a specific load. Keywords: "Carrier Rate Confirmation", Carrier Rate, Rate Confirmation, Carrier:',
            'cust_rate_confirmation': 'Customer Rate Confirmation - Rate confirmation directed at/issued to the customer. Keywords: "Customer Rate Confirmation", Customer Rate, Quote to',
            'clear_to_pay': 'Clear to Pay - Authorization indicating invoice is approved for payment. Keywords: "Clear to Pay", Approved for Payment, Payment Authorization, Clear to Pay Stamp',
            'scale_ticket': 'Scale Ticket - Weight record from a scale for vehicle/load (used for billing/compliance). Keywords: "Scale Ticket", Gross Weight, Tare, Net Weight, Weighed At',
            'log': 'Log - Activity record (driver log, tracking log) for operational/regulatory use. Keywords: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp',
            'fuel_receipt': 'Fuel Receipt - Receipt for fuel purchase (expense item). Keywords: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt',
            'combined_carrier_documents': 'Combined Carrier Documents - Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.). Keywords: multiple distinct document headers on one page, Bundle, Combined',
            'pack_list': 'Packing List - Itemized list of shipment contents for inventory/receiving checks. Keywords: Packing List, Pack List, Contents, Qty, Item Description',
            'po': 'Purchase Order - Buyer\'s order to a seller specifying items, qty, and price. Keywords: Purchase Order, PO#, Buyer, PO Number',
            'comm_invoice': 'Commercial Invoice - Customs-focused invoice for international shipments (value, HS codes). Keywords: Commercial Invoice, HS Code, Country of Origin, Customs Value',
            'customs_doc': 'Customs Document - General customs paperwork (declarations, certificates, permits). Keywords: Customs, Declaration, Import/Export Declaration, Customs Broker',
            'nmfc_cert': 'NMFC Certificate - Document showing National Motor Freight Classification codes/class assignments. Keywords: NMFC, National Motor Freight Classification, Class',
            'other': 'Other - Any logistics-related page that doesn\'t fit the above categories. Use sparingly. Keywords: none specific',
            'coa': 'Certificate of Analysis - Lab-quality report verifying composition/quality of goods. Keywords: Certificate of Analysis, COA, Test Results, Specifications',
            'tender_from_cust': 'Load Tender from Customer - Customer\'s load tender or request to a carrier to transport a load. Keywords: Load Tender, Tender, Tender from, Request to Carrier',
            'lumper_receipt': 'Lumper Receipt - Receipt for lumper services (loading/unloading labor). Keywords: Lumper, Lumper Receipt, Lumper Charge',
            'so_confirmation': 'Sales Order Confirmation - Seller\'s confirmation of a sales order (acknowledges order details). Keywords: Sales Order Confirmation, SO#, Order Confirmation',
            'po_confirmation': 'Purchase Order Confirmation - Seller\'s acceptance/confirmation of a buyer\'s PO. Keywords: Purchase Order Confirmation, PO Confirmation, Acknowledgement',
            'pre_pull_receipt': 'Pre-Pull Receipt - Proof/receipt for pre-pulling a container or load before scheduled pickup. Keywords: Pre-pull, Pre Pull, Prepull, Pre-Pull Receipt',
            'ingate': 'Ingate Document - Record of vehicle/container entering a facility (gate-in). Keywords: Ingate, Gate In, Time In, Truck In, Entry Time',
            'outgate': 'Outgate Document - Record of vehicle/container exiting a facility (gate-out/release). Keywords: Outgate, Gate Out, Time Out, Release, Exit Time'
        }
    
    def analyze_document_type(self, extracted_value, true_data_value, pdf_path, file_name):
        """
        Analyze document type based on common patterns and keywords
        Enhanced analysis with more detailed reasoning
        """
        doc_types = self.get_doc_type_definitions()
        
        # Check if PDF exists
        pdf_exists = os.path.exists(pdf_path)
        
        # Common misclassifications with detailed analysis
        common_misclassifications = {
            ('pack_list', 'pod'): {
                'result': 'Seems True',
                'reason': 'Packing List vs POD: POD contains delivery signatures and confirmation of receipt, while Packing List contains itemized contents. True data (POD) likely correct as these are commonly confused.'
            },
            ('bol', 'pod'): {
                'result': 'Seems True', 
                'reason': 'BOL vs POD: BOL is a shipping document with shipper/consignee details, POD is delivery confirmation with signatures. True data (POD) likely correct.'
            },
            ('comm_invoice', 'pod'): {
                'result': 'Seems True',
                'reason': 'Commercial Invoice vs POD: Commercial Invoice has customs/HS codes for international shipments, POD has delivery signatures. True data (POD) likely correct.'
            },
            ('invoice', 'pod'): {
                'result': 'Seems True',
                'reason': 'Invoice vs POD: Invoice contains billing amounts and charges, POD contains delivery confirmation signatures. True data (POD) likely correct.'
            },
            ('bol', 'invoice'): {
                'result': 'Seems True',
                'reason': 'BOL vs Invoice: BOL contains shipping details and cargo information, Invoice contains billing charges. True data (Invoice) likely correct.'
            },
            ('combined_carrier_documents', 'invoice'): {
                'result': 'Seems True',
                'reason': 'Combined Documents vs Invoice: If page shows single invoice rather than multiple bundled documents, True data (Invoice) likely correct.'
            },
            ('scale_ticket', 'pod'): {
                'result': 'Seems True',
                'reason': 'Scale Ticket vs POD: Scale Ticket shows weight measurements, POD shows delivery confirmation. True data (POD) likely correct.'
            },
            ('pod', 'bol'): {
                'result': 'Seems True',
                'reason': 'POD vs BOL: POD is delivery confirmation, BOL is shipping document. True data (BOL) likely correct.'
            },
            ('invoice', 'rate_confirmation'): {
                'result': 'Seems True',
                'reason': 'Invoice vs Rate Confirmation: Invoice is billing document, Rate Confirmation is agreement on rates. True data (Rate Confirmation) likely correct.'
            },
            ('invoice', 'tender_from_cust'): {
                'result': 'Seems True',
                'reason': 'Invoice vs Load Tender: Invoice is billing, Load Tender is customer request for transport. True data (Load Tender) likely correct.'
            },
            ('other', 'bol'): {
                'result': 'Seems True',
                'reason': 'Other vs BOL: If document has BOL characteristics (shipper/consignee, cargo details), True data (BOL) likely correct.'
            },
            ('other', 'pod'): {
                'result': 'Seems True',
                'reason': 'Other vs POD: If document has POD characteristics (delivery signatures, receipt confirmation), True data (POD) likely correct.'
            },
            ('scale_ticket', 'coa'): {
                'result': 'Unclear',
                'reason': 'Scale Ticket vs COA: Scale Ticket shows weights, COA shows quality analysis. Both are measurement documents but serve different purposes. Manual review recommended.'
            },
            ('combined_carrier_documents', 'rate_confirmation'): {
                'result': 'Seems True',
                'reason': 'Combined Documents vs Rate Confirmation: If page shows single rate confirmation rather than multiple documents, True data (Rate Confirmation) likely correct.'
            }
        }
        
        key = (extracted_value, true_data_value)
        reverse_key = (true_data_value, extracted_value)
        
        if key in common_misclassifications:
            analysis = common_misclassifications[key]
            pdf_note = f" PDF {'exists' if pdf_exists else 'not found'} at {pdf_path}."
            return analysis['result'], analysis['reason'] + pdf_note
        elif reverse_key in common_misclassifications:
            analysis = common_misclassifications[reverse_key]
            # Flip the result since we're looking at reverse
            flipped_result = 'Seems False' if analysis['result'] == 'Seems True' else analysis['result']
            pdf_note = f" PDF {'exists' if pdf_exists else 'not found'} at {pdf_path}."
            return flipped_result, f"Reverse of common pattern: {analysis['reason']}" + pdf_note
        else:
            pdf_note = f" PDF {'exists' if pdf_exists else 'not found'} at {pdf_path}."
            return "Unclear", f"Uncommon classification difference between {extracted_value} and {true_data_value}. Manual review recommended to determine correct classification." + pdf_note
    
    def run_analysis(self):
        """Run the complete analysis"""
        if not self.load_data():
            return False
        
        print(f"\nStarting Fast AI Cross Check Analysis")
        print(f"Total FALSE entries to analyze: {len(self.false_entries)}")
        print(f"Output file: {self.output_file}")
        
        # Create initial output file
        self.save_progress()
        
        # Process all entries
        for idx, (row_index, entry) in enumerate(self.false_entries.iterrows()):
            file_name = entry['File Name']
            extracted_value = entry['Extracted Value']
            true_data_value = entry['True Data Value']
            pdf_path = entry['Moved PDF Path']
            
            print(f"Analyzing {idx + 1}/{len(self.false_entries)}: {file_name} - {extracted_value} vs {true_data_value}")
            
            # Perform automated analysis
            ai_cross_check, comment = self.analyze_document_type(
                extracted_value, true_data_value, pdf_path, file_name
            )
            
            # Update the dataframe
            self.df.loc[self.df.index[row_index], 'AI Cross Check'] = ai_cross_check
            self.df.loc[self.df.index[row_index], 'AI Cross Check Comment'] = comment
            
            self.processed_count += 1
            
            # Save progress every 50 entries
            if self.processed_count % 50 == 0:
                self.save_progress()
                print(f"Progress saved! Processed {self.processed_count}/{len(self.false_entries)} entries")
        
        # Final save
        self.save_progress()
        print(f"\nAnalysis complete! Processed {self.processed_count}/{len(self.false_entries)} entries")
        
        # Show summary
        self.show_summary()
        return True
    
    def show_summary(self):
        """Show analysis summary"""
        false_entries = self.df[self.df['Correct'] == 'FALSE']
        processed_entries = false_entries[false_entries['AI Cross Check'] != '']
        
        print(f"\n{'='*60}")
        print("ANALYSIS SUMMARY")
        print(f"{'='*60}")
        print(f"Total FALSE entries: {len(false_entries)}")
        print(f"Processed entries: {len(processed_entries)}")
        print(f"Processing rate: {len(processed_entries)/len(false_entries)*100:.1f}%")
        
        if len(processed_entries) > 0:
            print(f"\nAI Cross Check Results:")
            results_summary = processed_entries['AI Cross Check'].value_counts()
            for result, count in results_summary.items():
                percentage = count / len(processed_entries) * 100
                print(f"  {result}: {count} ({percentage:.1f}%)")
            
            print(f"\nMost Common Misclassification Patterns:")
            # Group by extracted vs true value patterns
            patterns = processed_entries.groupby(['Extracted Value', 'True Data Value']).size().sort_values(ascending=False)
            for (extracted, true), count in patterns.head(10).items():
                print(f"  {extracted} → {true}: {count} cases")

def main():
    input_file = "data/evaluation/classification_evaluation_report.xlsx"
    output_file = "data/evaluation/classification_evaluation_report_with_ai_cross_check_fast.xlsx"
    
    # Create output directory if it doesn't exist
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    checker = FastAICrossChecker(input_file, output_file)
    checker.run_analysis()

if __name__ == "__main__":
    main()
